is_global = true
build_property.TargetFramework = net6.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = OpenAuth.Customer.Mvc
build_property.RootNamespace = OpenAuth.Customer.Mvc
build_property.ProjectDir = D:\代码\Web_OV2\OpenAuth.Customer.Mvc\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 6.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\代码\Web_OV2\OpenAuth.Customer.Mvc
build_property._RazorSourceGeneratorDebug = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/BasicManagement/Files/FilesManage.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQmFzaWNNYW5hZ2VtZW50XEZpbGVzXEZpbGVzTWFuYWdlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/BasicManagement/Files/FilesUpLoad.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQmFzaWNNYW5hZ2VtZW50XEZpbGVzXEZpbGVzVXBMb2FkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/BasicManagement/Files/NewOrEditName.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQmFzaWNNYW5hZ2VtZW50XEZpbGVzXE5ld09yRWRpdE5hbWUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Component/SelectModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcG9uZW50XFNlbGVjdE1vZGFsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Error/Auth.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRXJyb3JcQXV0aC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Home/git.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxnaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Home/Main.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxNYWluLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Login/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTG9naW5cSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/BOLSystem/BOLSystem.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XEJPTFN5c3RlbVxCT0xTeXN0ZW0uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/BOLSystem/BolSystemEdit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XEJPTFN5c3RlbVxCb2xTeXN0ZW1FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/BOLSystem/BolSystemExport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XEJPTFN5c3RlbVxCb2xTeXN0ZW1FeHBvcnQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/BOLSystem/BolSystemPlanning.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XEJPTFN5c3RlbVxCb2xTeXN0ZW1QbGFubmluZy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/BOLSystem/BolSystemUpdate.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XEJPTFN5c3RlbVxCb2xTeXN0ZW1VcGRhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/FlashReport/FlashReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XEZsYXNoUmVwb3J0XEZsYXNoUmVwb3J0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/EditDispatch.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XEVkaXREaXNwYXRjaC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/EditOrder.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XEVkaXRPcmRlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/InvboundDetail.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XEludmJvdW5kRGV0YWlsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/InvLoadData.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XEludkxvYWREYXRhLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/InvNoticeDetail.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XEludk5vdGljZURldGFpbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/LoadData.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XExvYWREYXRhLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/OrderDetail.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XE9yZGVyRGV0YWlsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/OrdersOverView.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XE9yZGVyc092ZXJWaWV3LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/OrderWarehouse.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XE9yZGVyV2FyZWhvdXNlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/OutboundDetail.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XE91dGJvdW5kRGV0YWlsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/OutNoticeDetail.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XE91dE5vdGljZURldGFpbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/OrderManagement/OrdersOverView/SplitOrder.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcT3JkZXJNYW5hZ2VtZW50XE9yZGVyc092ZXJWaWV3XFNwbGl0T3JkZXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Redirects/IdentityAuth.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVkaXJlY3RzXElkZW50aXR5QXV0aC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/UserManage/ChangePassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlck1hbmFnZVxDaGFuZ2VQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Warehouse/Inventory/EmailReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcV2FyZWhvdXNlXEludmVudG9yeVxFbWFpbFJlcG9ydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/Warehouse/Inventory/Inventory.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcV2FyZWhvdXNlXEludmVudG9yeVxJbnZlbnRvcnkuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/代码/Web_OV2/OpenAuth.Customer.Mvc/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
